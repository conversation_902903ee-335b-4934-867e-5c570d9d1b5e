"""
字典表业务逻辑处理
"""
import logging
from typing import Dict, Any, Optional

from models import get_db, Regions, DiseaseTypes, ManagementStatus
from tasks.registry import register_operation, operation_registry

logger = logging.getLogger(__name__)


class DictionaryBusiness:
    """字典表业务逻辑类"""

    def __init__(self):
        pass

    @register_operation('dictionary.get_regions')
    def get_regions(self, province: Optional[str] = None, city: Optional[str] = None) -> Dict[
        str, Any]:
        """
        获取地区列表
        
        Args:
            province: 省份过滤
            city: 城市过滤
            
        Returns:
            Dict: 地区列表
        """
        try:
            with get_db() as db:
                query = db.query(Regions).filter(Regions.is_active == True)

                # 省份过滤
                if province:
                    query = query.filter(Regions.province == province)

                # 城市过滤
                if city:
                    query = query.filter(Regions.city == city)

                regions = query.order_by(Regions.province, Regions.city,
                                         Regions.county_district).all()

                regions_list = []
                for region in regions:
                    regions_list.append({
                        'id': region.id,
                        'province': region.province,
                        'city': region.city,
                        'county_district': region.county_district,
                        'region_code': region.region_code,
                        'is_active': region.is_active
                    })

                return {
                    'success': True,
                    'message': '获取地区列表成功',
                    'data': regions_list
                }

        except Exception as e:
            logger.error(f"获取地区列表失败: {e}")
            return {'success': False, 'message': f'获取地区列表失败: {str(e)}'}

    @register_operation('dictionary.get_provinces')
    def get_provinces(self) -> Dict[str, Any]:
        """
        获取省份列表
        
        Returns:
            Dict: 省份列表
        """
        try:
            with get_db() as db:
                provinces = db.query(Regions.province).filter(
                    Regions.is_active == True
                ).distinct().order_by(Regions.province).all()

                provinces_list = [province[0] for province in provinces]

                return {
                    'success': True,
                    'message': '获取省份列表成功',
                    'data': provinces_list
                }

        except Exception as e:
            logger.error(f"获取省份列表失败: {e}")
            return {'success': False, 'message': f'获取省份列表失败: {str(e)}'}

    @register_operation('dictionary.get_cities')
    def get_cities(self, province: str) -> Dict[str, Any]:
        """
        根据省份获取城市列表

        Args:
            province: 省份名称

        Returns:
            Dict: 城市列表
        """
        try:
            with get_db() as db:
                cities = db.query(Regions.city).filter(
                    Regions.province == province,
                    Regions.is_active == True
                ).distinct().order_by(Regions.city).all()

                cities_list = [city[0] for city in cities]

                return {
                    'success': True,
                    'message': '获取城市列表成功',
                    'data': cities_list
                }

        except Exception as e:
            logger.error(f"获取城市列表失败: {e}")
            return {'success': False, 'message': f'获取城市列表失败: {str(e)}'}

    @register_operation('dictionary.get_counties')
    def get_counties(self, province: str, city: str) -> Dict[str, Any]:
        """
        根据省份和城市获取县区列表

        Args:
            province: 省份名称
            city: 城市名称

        Returns:
            Dict: 县区列表
        """
        try:
            with get_db() as db:
                counties = db.query(Regions).filter(
                    Regions.province == province,
                    Regions.city == city,
                    Regions.is_active == True
                ).order_by(Regions.county_district).all()

                counties_list = []
                for county in counties:
                    counties_list.append({
                        'id': county.id,
                        'county_district': county.county_district,
                        'region_code': county.region_code
                    })

                return {
                    'success': True,
                    'message': '获取县区列表成功',
                    'data': counties_list
                }

        except Exception as e:
            logger.error(f"获取县区列表失败: {e}")
            return {'success': False, 'message': f'获取县区列表失败: {str(e)}'}

    @register_operation('dictionary.get_disease_types')
    def get_disease_types(self, category: Optional[str] = None) -> Dict[str, Any]:
        """
        获取疾病类型列表
        
        Args:
            category: 疾病大类过滤
            
        Returns:
            Dict: 疾病类型列表
        """
        try:
            with get_db() as db:
                query = db.query(DiseaseTypes).filter(DiseaseTypes.is_active == True)

                # 疾病大类过滤
                if category:
                    query = query.filter(DiseaseTypes.category == category)

                disease_types = query.order_by(DiseaseTypes.category,
                                               DiseaseTypes.disease_name).all()

                disease_types_list = []
                for disease_type in disease_types:
                    disease_types_list.append({
                        'id': disease_type.id,
                        'disease_code': disease_type.disease_code,
                        'disease_name': disease_type.disease_name,
                        'category': disease_type.category,
                        'icd10_code': disease_type.icd10_code,
                        'is_active': disease_type.is_active,
                        'created_time': disease_type.created_time.strftime('%Y-%m-%d %H:%M:%S')
                        if disease_type.created_time else None
                    })

                return {
                    'success': True,
                    'message': '获取疾病类型列表成功',
                    'data': disease_types_list
                }

        except Exception as e:
            logger.error(f"获取疾病类型列表失败: {e}")
            return {'success': False, 'message': f'获取疾病类型列表失败: {str(e)}'}

    @register_operation('dictionary.get_disease_categories')
    def get_disease_categories(self) -> Dict[str, Any]:
        """
        获取疾病大类列表

        Returns:
            Dict: 疾病大类列表
        """
        try:
            with get_db() as db:
                categories = db.query(DiseaseTypes.category).filter(
                    DiseaseTypes.is_active == True
                ).distinct().order_by(DiseaseTypes.category).all()

                categories_list = [category[0] for category in categories]

                return {
                    'success': True,
                    'message': '获取疾病大类列表成功',
                    'data': categories_list
                }

        except Exception as e:
            logger.error(f"获取疾病大类列表失败: {e}")
            return {'success': False, 'message': f'获取疾病大类列表失败: {str(e)}'}

    @register_operation('dictionary.search_disease_types')
    def search_disease_types(self, keyword: str) -> Dict[str, Any]:
        """
        搜索疾病类型

        Args:
            keyword: 搜索关键词（疾病名称或编码）

        Returns:
            Dict: 搜索结果
        """
        try:
            with get_db() as db:
                disease_types = db.query(DiseaseTypes).filter(
                    DiseaseTypes.is_active == True,
                    (DiseaseTypes.disease_name.like(f'%{keyword}%') |
                     DiseaseTypes.disease_code.like(f'%{keyword}%') |
                     DiseaseTypes.icd10_code.like(f'%{keyword}%'))
                ).order_by(DiseaseTypes.disease_name).all()

                disease_types_list = []
                for disease_type in disease_types:
                    disease_types_list.append({
                        'id': disease_type.id,
                        'disease_code': disease_type.disease_code,
                        'disease_name': disease_type.disease_name,
                        'category': disease_type.category,
                        'icd10_code': disease_type.icd10_code
                    })

                return {
                    'success': True,
                    'message': '搜索疾病类型成功',
                    'data': disease_types_list
                }

        except Exception as e:
            logger.error(f"搜索疾病类型失败: {e}")
            return {'success': False, 'message': f'搜索疾病类型失败: {str(e)}'}

    @register_operation('dictionary.get_management_status')
    def get_management_status(self) -> Dict[str, Any]:
        """
        获取管理状态列表

        Returns:
            Dict: 管理状态列表
        """
        try:
            with get_db() as db:
                management_status = db.query(ManagementStatus).filter(
                    ManagementStatus.is_active == True
                ).order_by(ManagementStatus.status_name).all()

                status_list = []
                for status in management_status:
                    status_list.append({
                        'id': status.id,
                        'status_code': status.status_code,
                        'status_name': status.status_name,
                        'is_active': status.is_active
                    })

                return {
                    'success': True,
                    'message': '获取管理状态列表成功',
                    'data': status_list
                }

        except Exception as e:
            logger.error(f"获取管理状态列表失败: {e}")
            return {'success': False, 'message': f'获取管理状态列表失败: {str(e)}'}

    @register_operation('dictionary.get_region_by_id')
    def get_region_by_id(self, region_id: int) -> Dict[str, Any]:
        """
        根据ID获取地区信息
        
        Args:
            region_id: 地区ID
            
        Returns:
            Dict: 地区信息
        """
        try:
            with get_db() as db:
                region = db.query(Regions).filter(
                    Regions.id == region_id,
                    Regions.is_active == True
                ).first()

                if not region:
                    return {'success': False, 'message': '地区不存在'}

                region_dict = {
                    'id': region.id,
                    'province': region.province,
                    'city': region.city,
                    'county_district': region.county_district,
                    'region_code': region.region_code,
                    'is_active': region.is_active
                }

                return {
                    'success': True,
                    'message': '获取地区信息成功',
                    'data': region_dict
                }

        except Exception as e:
            logger.error(f"获取地区信息失败: {e}")
            return {'success': False, 'message': f'获取地区信息失败: {str(e)}'}

    @register_operation('dictionary.get_disease_type_by_id')
    def get_disease_type_by_id(self, disease_type_id: int) -> Dict[str, Any]:
        """
        根据ID获取疾病类型信息
        
        Args:
            disease_type_id: 疾病类型ID
            
        Returns:
            Dict: 疾病类型信息
        """
        try:
            with get_db() as db:
                disease_type = db.query(DiseaseTypes).filter(
                    DiseaseTypes.id == disease_type_id,
                    DiseaseTypes.is_active == True
                ).first()

                if not disease_type:
                    return {'success': False, 'message': '疾病类型不存在'}

                disease_type_dict = {
                    'id': disease_type.id,
                    'disease_code': disease_type.disease_code,
                    'disease_name': disease_type.disease_name,
                    'category': disease_type.category,
                    'icd10_code': disease_type.icd10_code,
                    'is_active': disease_type.is_active,
                    'created_time': disease_type.created_time.strftime('%Y-%m-%d %H:%M:%S')
                    if disease_type.created_time else None
                }

                return {
                    'success': True,
                    'message': '获取疾病类型信息成功',
                    'data': disease_type_dict
                }

        except Exception as e:
            logger.error(f"获取疾病类型信息失败: {e}")
            return {'success': False, 'message': f'获取疾病类型信息失败: {str(e)}'}


# 创建业务实例（重要：必须创建实例才能注册）
dictionary_business = DictionaryBusiness()
operation_registry.auto_bind_instance_methods(dictionary_business)

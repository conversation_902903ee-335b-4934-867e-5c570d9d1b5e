"""
医疗记录业务逻辑处理
"""
import logging
from datetime import datetime
from typing import Dict, Any

from sqlalchemy.orm import joinedload

from models import get_db, DiagnosisRecords, TreatDetail, Patients
from tasks.registry import register_operation, operation_registry

logger = logging.getLogger(__name__)


class MedicalBusiness:
    """医疗记录业务逻辑类"""

    def __init__(self):
        pass

    # ==================== 诊断记录相关操作 ====================

    @register_operation('medical.create_diagnosis_record')
    def create_diagnosis_record(self, record_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建诊断记录
        
        Args:
            record_data: 诊断记录数据
            
        Returns:
            Dict: 创建结果
        """
        try:
            with get_db() as db:
                # 验证必填字段
                required_fields = ['patient_id', 'visit_date']
                for field in required_fields:
                    if field not in record_data or not record_data[field]:
                        return {'success': False, 'message': f'缺少必填字段: {field}'}

                # 验证患者是否存在
                patient = db.query(Patients).filter(
                    Patients.id == record_data['patient_id'],
                    Patients.is_active == True
                ).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 创建诊断记录
                diagnosis_record = DiagnosisRecords(
                    patient_id=record_data['patient_id'],
                    visit_date=datetime.strptime(record_data['visit_date'], '%Y-%m-%d').date(),
                    diagnosis_type=record_data.get('diagnosis_type'),
                    diagnosis_result=record_data.get('diagnosis_result'),
                    attending_doctor=record_data.get('attending_doctor'),
                    treatment_plan=record_data.get('treatment_plan')
                )

                db.add(diagnosis_record)
                # 自动commit，无需手动处理

                return {
                    'success': True,
                    'message': '诊断记录创建成功',
                    'diagnosis_record_id': diagnosis_record.id
                }

        except Exception as e:
            logger.error(f"创建诊断记录失败: {e}")
            return {'success': False, 'message': f'创建诊断记录失败: {str(e)}'}

    @register_operation('medical.get_diagnosis_record')
    def get_diagnosis_record(self, record_id: int) -> Dict[str, Any]:
        """
        获取诊断记录详情
        
        Args:
            record_id: 诊断记录ID
            
        Returns:
            Dict: 诊断记录信息
        """
        try:
            with get_db() as db:
                record = db.query(DiagnosisRecords).options(
                    joinedload(DiagnosisRecords.patient)
                ).filter(DiagnosisRecords.id == record_id).first()

                if not record:
                    return {'success': False, 'message': '诊断记录不存在'}

                record_dict = self._diagnosis_record_to_dict(record)

                return {
                    'success': True,
                    'message': '获取诊断记录成功',
                    'data': record_dict
                }

        except Exception as e:
            logger.error(f"获取诊断记录失败: {e}")
            return {'success': False, 'message': f'获取诊断记录失败: {str(e)}'}

    @register_operation('medical.get_patient_diagnosis_records')
    def get_patient_diagnosis_records(self, patient_id: int, page: int = 1, page_size: int = 10) \
            -> \
                    Dict[str, Any]:
        """
        获取患者的诊断记录列表
        
        Args:
            patient_id: 患者ID
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 诊断记录列表
        """
        try:
            with get_db() as db:
                query = db.query(DiagnosisRecords).filter(
                    DiagnosisRecords.patient_id == patient_id
                ).order_by(DiagnosisRecords.visit_date.desc())

                # 分页
                total = query.count()
                records = query.offset((page - 1) * page_size).limit(page_size).all()

                records_list = [self._diagnosis_record_to_dict(record, include_patient=False)
                                for record in records]

                return {
                    'success': True,
                    'message': '获取患者诊断记录成功',
                    'data': {
                        'records': records_list,
                        'total': total,
                        'page': page,
                        'page_size': page_size,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }

        except Exception as e:
            logger.error(f"获取患者诊断记录失败: {e}")
            return {'success': False, 'message': f'获取患者诊断记录失败: {str(e)}'}

    @register_operation('medical.update_diagnosis_record')
    def update_diagnosis_record(self, record_id: int, record_data: Dict[str, Any]) -> Dict[
        str, Any]:
        """
        更新诊断记录
        
        Args:
            record_id: 诊断记录ID
            record_data: 更新的记录数据
            
        Returns:
            Dict: 更新结果
        """
        try:
            with get_db() as db:
                record = db.query(DiagnosisRecords).filter(
                    DiagnosisRecords.id == record_id
                ).first()

                if not record:
                    return {'success': False, 'message': '诊断记录不存在'}

                # 更新字段
                updatable_fields = [
                    'visit_date', 'diagnosis_type', 'diagnosis_result',
                    'attending_doctor', 'treatment_plan'
                ]

                for field in updatable_fields:
                    if field in record_data:
                        if field == 'visit_date' and isinstance(record_data[field], str):
                            setattr(record, field,
                                    datetime.strptime(record_data[field], '%Y-%m-%d').date())
                        else:
                            setattr(record, field, record_data[field])

                # 自动commit，无需手动处理

                return {'success': True, 'message': '诊断记录更新成功'}

        except Exception as e:
            logger.error(f"更新诊断记录失败: {e}")
            return {'success': False, 'message': f'更新诊断记录失败: {str(e)}'}

    @register_operation('medical.delete_diagnosis_record')
    def delete_diagnosis_record(self, record_id: int) -> Dict[str, Any]:
        """
        删除诊断记录
        
        Args:
            record_id: 诊断记录ID
            
        Returns:
            Dict: 删除结果
        """
        try:
            with get_db() as db:
                record = db.query(DiagnosisRecords).filter(
                    DiagnosisRecords.id == record_id
                ).first()

                if not record:
                    return {'success': False, 'message': '诊断记录不存在'}

                db.delete(record)
                # 自动commit，无需手动处理

                return {'success': True, 'message': '诊断记录删除成功'}

        except Exception as e:
            logger.error(f"删除诊断记录失败: {e}")
            return {'success': False, 'message': f'删除诊断记录失败: {str(e)}'}

    # ==================== 治疗详情相关操作 ====================

    @register_operation('medical.create_treat_detail')
    def create_treat_detail(self, treat_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建治疗详情
        
        Args:
            treat_data: 治疗详情数据
            
        Returns:
            Dict: 创建结果
        """
        try:
            with get_db() as db:
                # 验证必填字段
                required_fields = ['patient_id', 'treat_doctor']
                for field in required_fields:
                    if field not in treat_data or not treat_data[field]:
                        return {'success': False, 'message': f'缺少必填字段: {field}'}

                # 验证患者是否存在
                patient = db.query(Patients).filter(
                    Patients.id == treat_data['patient_id'],
                    Patients.is_active == True
                ).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 创建治疗详情
                treat_detail = TreatDetail(
                    patient_id=treat_data['patient_id'],
                    treat_status=treat_data.get('treat_status'),
                    management_status_id=treat_data.get('management_status_id'),
                    treat_plan=treat_data.get('treat_plan'),
                    treat_doctor=treat_data['treat_doctor'],
                    treat_dept=treat_data.get('treat_dept'),
                    treat_time=datetime.strptime(treat_data['treat_time'], '%Y-%m-%d %H:%M:%S')
                    if treat_data.get('treat_time') else datetime.now()
                )

                db.add(treat_detail)
                # 自动commit，无需手动处理

                return {
                    'success': True,
                    'message': '治疗详情创建成功',
                    'treat_detail_id': treat_detail.id
                }

        except Exception as e:
            logger.error(f"创建治疗详情失败: {e}")
            return {'success': False, 'message': f'创建治疗详情失败: {str(e)}'}

    @register_operation('medical.get_treat_detail')
    def get_treat_detail(self, treat_id: int) -> Dict[str, Any]:
        """
        获取治疗详情
        
        Args:
            treat_id: 治疗详情ID
            
        Returns:
            Dict: 治疗详情信息
        """
        try:
            with get_db() as db:
                treat_detail = db.query(TreatDetail).options(
                    joinedload(TreatDetail.patient),
                    joinedload(TreatDetail.management_status)
                ).filter(TreatDetail.id == treat_id).first()

                if not treat_detail:
                    return {'success': False, 'message': '治疗详情不存在'}

                treat_dict = self._treat_detail_to_dict(treat_detail)

                return {
                    'success': True,
                    'message': '获取治疗详情成功',
                    'data': treat_dict
                }

        except Exception as e:
            logger.error(f"获取治疗详情失败: {e}")
            return {'success': False, 'message': f'获取治疗详情失败: {str(e)}'}

    def _diagnosis_record_to_dict(self, record: DiagnosisRecords, include_patient: bool = True) -> \
            Dict[str, Any]:
        """将诊断记录对象转换为字典"""
        record_dict = {
            'id': record.id,
            'patient_id': record.patient_id,
            'visit_date': record.visit_date.strftime('%Y-%m-%d') if record.visit_date else None,
            'diagnosis_type': record.diagnosis_type,
            'diagnosis_result': record.diagnosis_result,
            'attending_doctor': record.attending_doctor,
            'treatment_plan': record.treatment_plan,
            'created_time': record.created_time.strftime(
                '%Y-%m-%d %H:%M:%S') if record.created_time else None
        }

        # 添加患者信息
        if include_patient and record.patient:
            record_dict['patient'] = {
                'id': record.patient.id,
                'name': record.patient.name,
                'patient_code': record.patient.patient_code,
                'id_card': record.patient.id_card,
                'phone': record.patient.phone
            }

        return record_dict

    def _treat_detail_to_dict(self, treat_detail: TreatDetail, include_patient: bool = True) -> \
            Dict[str, Any]:
        """将治疗详情对象转换为字典"""
        treat_dict = {
            'id': treat_detail.id,
            'patient_id': treat_detail.patient_id,
            'treat_status': treat_detail.treat_status,
            'management_status_id': treat_detail.management_status_id,
            'treat_plan': treat_detail.treat_plan,
            'treat_doctor': treat_detail.treat_doctor,
            'treat_dept': treat_detail.treat_dept,
            'treat_time': treat_detail.treat_time.strftime(
                '%Y-%m-%d %H:%M:%S') if treat_detail.treat_time else None,
            'created_time': treat_detail.created_time.strftime(
                '%Y-%m-%d %H:%M:%S') if treat_detail.created_time else None
        }

        # 添加患者信息
        if include_patient and treat_detail.patient:
            treat_dict['patient'] = {
                'id': treat_detail.patient.id,
                'name': treat_detail.patient.name,
                'patient_code': treat_detail.patient.patient_code,
                'id_card': treat_detail.patient.id_card,
                'phone': treat_detail.patient.phone
            }

        # 添加管理状态信息
        if treat_detail.management_status:
            treat_dict['management_status'] = {
                'id': treat_detail.management_status.id,
                'status_code': treat_detail.management_status.status_code,
                'status_name': treat_detail.management_status.status_name
            }

        return treat_dict

    @register_operation('medical.get_patient_treat_details')
    def get_patient_treat_details(self, patient_id: int, page: int = 1, page_size: int = 10) -> \
            Dict[str, Any]:
        """
        获取患者的治疗详情列表

        Args:
            patient_id: 患者ID
            page: 页码
            page_size: 每页大小

        Returns:
            Dict: 治疗详情列表
        """
        try:
            with get_db() as db:
                query = db.query(TreatDetail).options(
                    joinedload(TreatDetail.management_status)
                ).filter(
                    TreatDetail.patient_id == patient_id
                ).order_by(TreatDetail.treat_time.desc())

                # 分页
                total = query.count()
                treat_details = query.offset((page - 1) * page_size).limit(page_size).all()

                treat_details_list = [
                    self._treat_detail_to_dict(treat_detail, include_patient=False)
                    for treat_detail in treat_details]

                return {
                    'success': True,
                    'message': '获取患者治疗详情成功',
                    'data': {
                        'treat_details': treat_details_list,
                        'total': total,
                        'page': page,
                        'page_size': page_size,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }

        except Exception as e:
            logger.error(f"获取患者治疗详情失败: {e}")
            return {'success': False, 'message': f'获取患者治疗详情失败: {str(e)}'}

    @register_operation('medical.update_treat_detail')
    def update_treat_detail(self, treat_id: int, treat_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新治疗详情

        Args:
            treat_id: 治疗详情ID
            treat_data: 更新的治疗数据

        Returns:
            Dict: 更新结果
        """
        try:
            with get_db() as db:
                treat_detail = db.query(TreatDetail).filter(
                    TreatDetail.id == treat_id
                ).first()

                if not treat_detail:
                    return {'success': False, 'message': '治疗详情不存在'}

                # 更新字段
                updatable_fields = [
                    'treat_status', 'management_status_id', 'treat_plan',
                    'treat_doctor', 'treat_dept', 'treat_time'
                ]

                for field in updatable_fields:
                    if field in treat_data:
                        if field == 'treat_time' and isinstance(treat_data[field], str):
                            setattr(treat_detail, field,
                                    datetime.strptime(treat_data[field], '%Y-%m-%d %H:%M:%S'))
                        else:
                            setattr(treat_detail, field, treat_data[field])

                # 自动commit，无需手动处理

                return {'success': True, 'message': '治疗详情更新成功'}

        except Exception as e:
            logger.error(f"更新治疗详情失败: {e}")
            return {'success': False, 'message': f'更新治疗详情失败: {str(e)}'}

    @register_operation('medical.delete_treat_detail')
    def delete_treat_detail(self, treat_id: int) -> Dict[str, Any]:
        """
        删除治疗详情

        Args:
            treat_id: 治疗详情ID

        Returns:
            Dict: 删除结果
        """
        try:
            with get_db() as db:
                treat_detail = db.query(TreatDetail).filter(
                    TreatDetail.id == treat_id
                ).first()

                if not treat_detail:
                    return {'success': False, 'message': '治疗详情不存在'}

                db.delete(treat_detail)
                # 自动commit，无需手动处理

                return {'success': True, 'message': '治疗详情删除成功'}

        except Exception as e:
            logger.error(f"删除治疗详情失败: {e}")
            return {'success': False, 'message': f'删除治疗详情失败: {str(e)}'}


# 创建业务实例（重要：必须创建实例才能注册）
medical_business = MedicalBusiness()
operation_registry.auto_bind_instance_methods(medical_business)

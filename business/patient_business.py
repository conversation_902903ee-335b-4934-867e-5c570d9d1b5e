"""
患者业务逻辑处理
"""
import logging
from datetime import datetime, date
from typing import Dict, Any, Optional

from sqlalchemy import or_, func
from sqlalchemy.orm import joinedload

from models import get_db, Patients, PatientChronicDiseases
from tasks.registry import register_operation, operation_registry

logger = logging.getLogger(__name__)


class PatientBusiness:
    """患者业务逻辑类"""

    def __init__(self):
        pass

    @register_operation('patient.create')
    def create_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建患者
        
        Args:
            patient_data: 患者数据字典
            
        Returns:
            Dict: 创建结果
        """
        try:
            with get_db() as db:
                # 验证必填字段
                required_fields = ['name', 'birth_date', 'id_card', 'age']
                for field in required_fields:
                    if field not in patient_data or not patient_data[field]:
                        return {'success': False, 'message': f'缺少必填字段: {field}'}

                # 检查身份证号是否已存在
                existing_patient = db.query(Patients).filter(
                    Patients.id_card == patient_data['id_card']
                ).first()

                if existing_patient:
                    return {'success': False, 'message': '身份证号已存在'}

                # 生成患者编号
                patient_code = self._generate_patient_code(db)

                # 创建患者对象
                patient = Patients(
                    patient_code=patient_code,
                    name=patient_data['name'],
                    gender=patient_data.get('gender'),
                    birth_date=datetime.strptime(patient_data['birth_date'], '%Y-%m-%d').date(),
                    age=patient_data['age'],
                    id_card=patient_data['id_card'],
                    phone=patient_data.get('phone'),
                    region_id=patient_data.get('region_id'),
                    address=patient_data.get('address'),
                    emergency_contact=patient_data.get('emergency_contact'),
                    emergency_phone=patient_data.get('emergency_phone'),
                    occupation=patient_data.get('occupation'),
                    education=patient_data.get('education'),
                    marital_status=patient_data.get('marital_status'),
                    insurance_type=patient_data.get('insurance_type')
                )

                db.add(patient)
                # 自动commit，无需手动处理

                return {
                    'success': True,
                    'message': '患者创建成功',
                    'patient_id': patient.id,
                    'patient_code': patient.patient_code
                }

        except Exception as e:
            logger.error(f"创建患者失败: {e}")
            return {'success': False, 'message': f'创建患者失败: {str(e)}'}

    @register_operation('patient.get_by_id')
    def get_patient_by_id(self, patient_id: int) -> Dict[str, Any]:
        """
        根据ID获取患者信息
        
        Args:
            patient_id: 患者ID
            
        Returns:
            Dict: 患者信息
        """
        try:
            with get_db() as db:
                patient = db.query(Patients).options(
                    joinedload(Patients.region),
                    joinedload(Patients.chronic_diseases).joinedload(
                        PatientChronicDiseases.disease_type),
                    joinedload(Patients.chronic_diseases).joinedload(
                        PatientChronicDiseases.management_status)
                ).filter(Patients.id == patient_id, Patients.is_active == True).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 转换为字典格式
                patient_dict = self._patient_to_dict(patient)

                return {
                    'success': True,
                    'message': '获取患者信息成功',
                    'data': patient_dict
                }

        except Exception as e:
            logger.error(f"获取患者信息失败: {e}")
            return {'success': False, 'message': f'获取患者信息失败: {str(e)}'}

    @register_operation('patient.search')
    def search_patients(self, keyword: str = '', page: int = 1, page_size: int = 10,
                        region_id: Optional[int] = None) -> Dict[str, Any]:
        """
        搜索患者
        
        Args:
            keyword: 搜索关键词（姓名、身份证号、电话）
            page: 页码
            page_size: 每页大小
            region_id: 地区ID过滤
            
        Returns:
            Dict: 搜索结果
        """
        try:
            with get_db() as db:
                query = db.query(Patients).options(
                    joinedload(Patients.region)
                ).filter(Patients.is_active == True)

                # 关键词搜索
                if keyword:
                    query = query.filter(
                        or_(
                            Patients.name.like(f'%{keyword}%'),
                            Patients.id_card.like(f'%{keyword}%'),
                            Patients.phone.like(f'%{keyword}%'),
                            Patients.patient_code.like(f'%{keyword}%')
                        )
                    )

                # 地区过滤
                if region_id:
                    query = query.filter(Patients.region_id == region_id)

                # 分页
                total = query.count()
                patients = query.offset((page - 1) * page_size).limit(page_size).all()

                # 转换为字典格式
                patients_list = [self._patient_to_dict(patient, include_chronic_diseases=False)
                                 for patient in patients]

                return {
                    'success': True,
                    'message': '搜索患者成功',
                    'data': {
                        'patients': patients_list,
                        'total': total,
                        'page': page,
                        'page_size': page_size,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }

        except Exception as e:
            logger.error(f"搜索患者失败: {e}")
            return {'success': False, 'message': f'搜索患者失败: {str(e)}'}

    @register_operation('patient.update')
    def update_patient(self, patient_id: int, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新患者信息
        
        Args:
            patient_id: 患者ID
            patient_data: 更新的患者数据
            
        Returns:
            Dict: 更新结果
        """
        try:
            with get_db() as db:
                patient = db.query(Patients).filter(
                    Patients.id == patient_id, Patients.is_active == True
                ).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 如果更新身份证号，检查是否重复
                if 'id_card' in patient_data and patient_data['id_card'] != patient.id_card:
                    existing_patient = db.query(Patients).filter(
                        Patients.id_card == patient_data['id_card'],
                        Patients.id != patient_id
                    ).first()

                    if existing_patient:
                        return {'success': False, 'message': '身份证号已存在'}

                # 更新字段
                updatable_fields = [
                    'name', 'gender', 'birth_date', 'age', 'id_card', 'phone',
                    'region_id', 'address', 'emergency_contact', 'emergency_phone',
                    'occupation', 'education', 'marital_status', 'insurance_type'
                ]

                for field in updatable_fields:
                    if field in patient_data:
                        if field == 'birth_date' and isinstance(patient_data[field], str):
                            setattr(patient, field,
                                    datetime.strptime(patient_data[field], '%Y-%m-%d').date())
                        else:
                            setattr(patient, field, patient_data[field])

                # 自动commit，无需手动处理

                return {'success': True, 'message': '患者信息更新成功'}

        except Exception as e:
            logger.error(f"更新患者信息失败: {e}")
            return {'success': False, 'message': f'更新患者信息失败: {str(e)}'}

    @register_operation('patient.delete')
    def delete_patient(self, patient_id: int) -> Dict[str, Any]:
        """
        删除患者（软删除）
        
        Args:
            patient_id: 患者ID
            
        Returns:
            Dict: 删除结果
        """
        try:
            with get_db() as db:
                patient = db.query(Patients).filter(
                    Patients.id == patient_id, Patients.is_active == True
                ).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 软删除
                patient.is_active = False
                # 自动commit，无需手动处理

                return {'success': True, 'message': '患者删除成功'}

        except Exception as e:
            logger.error(f"删除患者失败: {e}")
            return {'success': False, 'message': f'删除患者失败: {str(e)}'}

    def _generate_patient_code(self, db=None) -> str:
        """生成患者编号"""
        today = datetime.now().strftime('%Y%m%d')

        # 如果没有传入db，则创建临时会话
        if db is None:
            with get_db() as temp_db:
                count = temp_db.query(func.count(Patients.id)).filter(
                    func.date(Patients.created_time) == date.today()
                ).scalar()
        else:
            # 查询今天已有的患者数量
            count = db.query(func.count(Patients.id)).filter(
                func.date(Patients.created_time) == date.today()
            ).scalar()

        return f"P{today}{count + 1:04d}"

    def _patient_to_dict(self, patient: Patients, include_chronic_diseases: bool = True) -> Dict[
        str, Any]:
        """将患者对象转换为字典"""
        patient_dict = {
            'id': patient.id,
            'patient_code': patient.patient_code,
            'name': patient.name,
            'gender': patient.gender,
            'birth_date': patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else None,
            'age': patient.age,
            'id_card': patient.id_card,
            'phone': patient.phone,
            'region_id': patient.region_id,
            'address': patient.address,
            'emergency_contact': patient.emergency_contact,
            'emergency_phone': patient.emergency_phone,
            'occupation': patient.occupation,
            'education': patient.education,
            'marital_status': patient.marital_status,
            'insurance_type': patient.insurance_type,
            'is_active': patient.is_active,
            'created_time': patient.created_time.strftime(
                '%Y-%m-%d %H:%M:%S') if patient.created_time else None,
            'updated_time': patient.updated_time.strftime(
                '%Y-%m-%d %H:%M:%S') if patient.updated_time else None
        }

        # 添加地区信息
        if patient.region:
            patient_dict['region'] = {
                'id': patient.region.id,
                'province': patient.region.province,
                'city': patient.region.city,
                'county_district': patient.region.county_district,
                'region_code': patient.region.region_code
            }

        # 添加慢性病信息
        if include_chronic_diseases and patient.chronic_diseases:
            patient_dict['chronic_diseases'] = []
            for cd in patient.chronic_diseases:
                if cd.is_active:
                    cd_dict = {
                        'id': cd.id,
                        'disease_type_id': cd.disease_type_id,
                        'visit_date': cd.visit_date.strftime('%Y-%m-%d') if cd.visit_date else None,
                        'management_status_id': cd.management_status_id,
                        'responsible_doctor': cd.responsible_doctor,
                        'severity_level': cd.severity_level,
                        'created_time': cd.created_time.strftime(
                            '%Y-%m-%d %H:%M:%S') if cd.created_time else None
                    }

                    # 添加疾病类型信息
                    if cd.disease_type:
                        cd_dict['disease_type'] = {
                            'id': cd.disease_type.id,
                            'disease_code': cd.disease_type.disease_code,
                            'disease_name': cd.disease_type.disease_name,
                            'category': cd.disease_type.category,
                            'icd10_code': cd.disease_type.icd10_code
                        }

                    # 添加管理状态信息
                    if cd.management_status:
                        cd_dict['management_status'] = {
                            'id': cd.management_status.id,
                            'status_code': cd.management_status.status_code,
                            'status_name': cd.management_status.status_name
                        }

                    patient_dict['chronic_diseases'].append(cd_dict)

        return patient_dict

    @register_operation('patient.add_chronic_disease')
    def add_chronic_disease(self, patient_id: int, disease_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        为患者添加慢性病

        Args:
            patient_id: 患者ID
            disease_data: 慢性病数据

        Returns:
            Dict: 添加结果
        """
        try:
            with get_db() as db:
                # 验证患者是否存在
                patient = db.query(Patients).filter(
                    Patients.id == patient_id, Patients.is_active == True
                ).first()

                if not patient:
                    return {'success': False, 'message': '患者不存在'}

                # 验证必填字段
                if 'disease_type_id' not in disease_data:
                    return {'success': False, 'message': '缺少疾病类型ID'}

                # 检查是否已存在相同的慢性病
                existing_disease = db.query(PatientChronicDiseases).filter(
                    PatientChronicDiseases.patient_id == patient_id,
                    PatientChronicDiseases.disease_type_id == disease_data['disease_type_id'],
                    PatientChronicDiseases.is_active == True
                ).first()

                if existing_disease:
                    return {'success': False, 'message': '患者已存在该慢性病记录'}

                # 创建慢性病记录
                chronic_disease = PatientChronicDiseases(
                    patient_id=patient_id,
                    disease_type_id=disease_data['disease_type_id'],
                    visit_date=datetime.strptime(disease_data['visit_date'], '%Y-%m-%d').date()
                    if disease_data.get('visit_date') else None,
                    management_status_id=disease_data.get('management_status_id'),
                    responsible_doctor=disease_data.get('responsible_doctor'),
                    severity_level=disease_data.get('severity_level')
                )

                db.add(chronic_disease)
                # 自动commit，无需手动处理

                return {
                    'success': True,
                    'message': '慢性病添加成功',
                    'chronic_disease_id': chronic_disease.id
                }

        except Exception as e:
            logger.error(f"添加慢性病失败: {e}")
            return {'success': False, 'message': f'添加慢性病失败: {str(e)}'}

    @register_operation('patient.update_chronic_disease')
    def update_chronic_disease(self, chronic_disease_id: int, disease_data: Dict[str, Any]) -> Dict[
        str, Any]:
        """
        更新患者慢性病信息

        Args:
            chronic_disease_id: 慢性病记录ID
            disease_data: 更新的慢性病数据

        Returns:
            Dict: 更新结果
        """
        try:
            with get_db() as db:
                chronic_disease = db.query(PatientChronicDiseases).filter(
                    PatientChronicDiseases.id == chronic_disease_id,
                    PatientChronicDiseases.is_active == True
                ).first()

                if not chronic_disease:
                    return {'success': False, 'message': '慢性病记录不存在'}

                # 更新字段
                updatable_fields = [
                    'disease_type_id', 'visit_date', 'management_status_id',
                    'responsible_doctor', 'severity_level'
                ]

                for field in updatable_fields:
                    if field in disease_data:
                        if field == 'visit_date' and isinstance(disease_data[field], str):
                            setattr(chronic_disease, field,
                                    datetime.strptime(disease_data[field], '%Y-%m-%d').date())
                        else:
                            setattr(chronic_disease, field, disease_data[field])

                # 自动commit，无需手动处理

                return {'success': True, 'message': '慢性病信息更新成功'}

        except Exception as e:
            logger.error(f"更新慢性病信息失败: {e}")
            return {'success': False, 'message': f'更新慢性病信息失败: {str(e)}'}

    @register_operation('patient.remove_chronic_disease')
    def remove_chronic_disease(self, chronic_disease_id: int) -> Dict[str, Any]:
        """
        移除患者慢性病（软删除）

        Args:
            chronic_disease_id: 慢性病记录ID

        Returns:
            Dict: 删除结果
        """
        try:
            with get_db() as db:
                chronic_disease = db.query(PatientChronicDiseases).filter(
                    PatientChronicDiseases.id == chronic_disease_id,
                    PatientChronicDiseases.is_active == True
                ).first()

                if not chronic_disease:
                    return {'success': False, 'message': '慢性病记录不存在'}

                # 软删除
                chronic_disease.is_active = False
                # 自动commit，无需手动处理

                return {'success': True, 'message': '慢性病记录删除成功'}

        except Exception as e:
            logger.error(f"删除慢性病记录失败: {e}")
            return {'success': False, 'message': f'删除慢性病记录失败: {str(e)}'}

    @register_operation('patient.get_chronic_diseases')
    def get_patient_chronic_diseases(self, patient_id: int) -> Dict[str, Any]:
        """
        获取患者的所有慢性病

        Args:
            patient_id: 患者ID

        Returns:
            Dict: 慢性病列表
        """
        try:
            with get_db() as db:
                chronic_diseases = db.query(PatientChronicDiseases).options(
                    joinedload(PatientChronicDiseases.disease_type),
                    joinedload(PatientChronicDiseases.management_status)
                ).filter(
                    PatientChronicDiseases.patient_id == patient_id,
                    PatientChronicDiseases.is_active == True
                ).all()

                diseases_list = []
                for cd in chronic_diseases:
                    cd_dict = {
                        'id': cd.id,
                        'disease_type_id': cd.disease_type_id,
                        'visit_date': cd.visit_date.strftime('%Y-%m-%d') if cd.visit_date else None,
                        'management_status_id': cd.management_status_id,
                        'responsible_doctor': cd.responsible_doctor,
                        'severity_level': cd.severity_level,
                        'created_time': cd.created_time.strftime(
                            '%Y-%m-%d %H:%M:%S') if cd.created_time else None
                    }

                    # 添加疾病类型信息
                    if cd.disease_type:
                        cd_dict['disease_type'] = {
                            'id': cd.disease_type.id,
                            'disease_code': cd.disease_type.disease_code,
                            'disease_name': cd.disease_type.disease_name,
                            'category': cd.disease_type.category,
                            'icd10_code': cd.disease_type.icd10_code
                        }

                    # 添加管理状态信息
                    if cd.management_status:
                        cd_dict['management_status'] = {
                            'id': cd.management_status.id,
                            'status_code': cd.management_status.status_code,
                            'status_name': cd.management_status.status_name
                        }

                    diseases_list.append(cd_dict)

                return {
                    'success': True,
                    'message': '获取患者慢性病成功',
                    'data': diseases_list
                }

        except Exception as e:
            logger.error(f"获取患者慢性病失败: {e}")
            return {'success': False, 'message': f'获取患者慢性病失败: {str(e)}'}


# 创建业务实例（重要：必须创建实例才能注册）
patient_business = PatientBusiness()
operation_registry.auto_bind_instance_methods(patient_business)

from urllib.parse import quote

from sqlalchemy import Column, DateTime, func, create_engine
from sqlalchemy.orm import declared_attr, declarative_base, sessionmaker, scoped_session

from db.mysql import db_config


class BaseModel:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    created_time = Column(DateTime, default=func.now(), comment="创建时间")
    updated_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")


# 创建Base对象，作为其他模型依赖的基础
Base = declarative_base(cls=BaseModel)

engine = create_engine(
    f'mysql+pymysql://{db_config.user}:{quote(db_config.password)}@{db_config.host}:'
    f'{db_config.port}/{db_config.db}?charset=utf8mb4',
    echo=True,  # 开发环境显示SQL日志
    future=True,
    pool_pre_ping=True,  # 连接池预检查
    pool_recycle=3600,  # 连接回收时间
    pool_size=30,  # 连接池大小
    max_overflow=20,  # 最大溢出连接数
)

# 创建Session工厂，配置连接池和事务参数
SessionLocal = sessionmaker(
    autocommit=False,  # 是否自动提交事务
    autoflush=False,  # 是否自动刷新
    bind=engine,  # 绑定到特定的数据库引擎
)

# 创建线程安全的Session对象
Session = scoped_session(SessionLocal)


# 数据库会话上下文管理器
def get_db():
    """获取数据库会话的上下文管理器"""
    db = Session()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# 创建所有的表
def init_db():
    Base.metadata.create_all(bind=engine)


if __name__ == "__main__":
    from models import *

    init_db()
